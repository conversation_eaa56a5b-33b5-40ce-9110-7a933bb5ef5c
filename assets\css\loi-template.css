body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.letterhead {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    max-width: 200px;
    margin-bottom: 10px;
}

h1 {
    text-align: center;
    font-size: 24px;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.subtitle {
    text-align: center;
    font-style: italic;
    margin-bottom: 30px;
}

.date-section {
    margin-bottom: 20px;
}

.date-section p {
    margin: 5px 0;
}

.section {
    margin-bottom: 20px;
}

.section h2 {
    font-size: 18px;
    margin-bottom: 10px;
}

.section p, .section ul {
    margin-bottom: 10px;
}

.section ul {
    padding-left: 20px;
}

.section ul li {
    margin-bottom: 8px;
}

.signature-section {
    margin-top: 40px;
}

.signature-table {
    width: 100%;
    border-collapse: collapse;
}

.signature-table td {
    padding: 10px 5px;
    border-bottom: 1px solid #ccc;
}

.signature-label {
    font-weight: bold;
    font-size: 14px;
    color: #666;
}

.footer {
    margin-top: 40px;
    text-align: center;
    font-style: italic;
    color: #666;
}

@media print {
    body {
        padding: 0;
    }

    .button-container,
    .print-button,
    .save-button,
    p[style*="text-align: center; margin-bottom: 1.5rem;"] {
        display: none !important;
    }

    /* Remove dashed underlines from editable fields when printing */
    .editable-field {
        border-bottom: none;
        padding: 0;
    }

    /* Add more space at the top of the page */
    .letterhead {
        margin-top: 20px;
    }
}

.print-button {
    display: block;
    margin: 20px auto;
    padding: 10px 20px;
    background-color: #4a6fa5;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

.print-button:hover {
    background-color: #166088;
}

.editable-field {
    padding: 2px 5px;
    border-bottom: 1px dashed #999;
    min-width: 50px;
    display: inline-block;
    color: #166088;
}

.editable-field:hover {
    background-color: rgba(74, 111, 165, 0.1);
}

.editable-field:focus {
    outline: none;
    background-color: rgba(74, 111, 165, 0.2);
    border-bottom: 1px solid #166088;
}

.button-container {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 20px 0;
}

.save-button {
    display: block;
    padding: 10px 20px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

.save-button:hover {
    background-color: #218838;
}
