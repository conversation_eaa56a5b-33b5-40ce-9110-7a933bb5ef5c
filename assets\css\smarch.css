:root {
    --primary-color: #4a6fa5;
    --secondary-color: #166088;
    --accent-color: #4fc3f7;
    --highlight-color: #ff6b6b;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --text-color: #333;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

header {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
}

.logo {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
}

.logo span {
    color: var(--secondary-color);
}

.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(74, 111, 165, 0.15) 0%, rgba(22, 96, 136, 0.15) 100%);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(255, 107, 107, 0.2) 100%);
    z-index: -1;
}

.hero::after {
    content: '';
    position: absolute;
    bottom: -50px;
    left: -50px;
    width: 250px;
    height: 250px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(79, 195, 247, 0.1) 0%, rgba(79, 195, 247, 0.2) 100%);
    z-index: -1;
}

.hero-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-logo {
    width: 180px;
    height: 180px;
    margin-bottom: 30px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 72px;
    font-weight: bold;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.hero-logo::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.hero-logo::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: -10px;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

h1 {
    font-size: 48px;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.tagline {
    font-size: 24px;
    margin-bottom: 30px;
    color: var(--secondary-color);
}

.description {
    font-size: 18px;
    margin-bottom: 40px;
    max-width: 600px;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 12px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    font-size: 18px;
    transition: all 0.3s ease;
    margin-bottom: 40px;
    border: none;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    z-index: -1;
    transition: opacity 0.3s ease;
    opacity: 0;
}

.cta-button:hover::before {
    opacity: 1;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.coming-soon {
    font-size: 20px;
    font-weight: 600;
    color: var(--accent-color);
    margin-bottom: 20px;
    letter-spacing: 1px;
}

.updates {
    margin: 80px 0;
    display: none; /* Hidden initially */
}

.updates h2 {
    text-align: center;
    margin-bottom: 40px;
    color: var(--primary-color);
    font-size: 36px;
}

.update-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
    margin-bottom: 30px;
}

.update-date {
    font-size: 14px;
    color: var(--accent-color);
    margin-bottom: 10px;
}

.update-title {
    font-size: 24px;
    margin-bottom: 15px;
    color: var(--secondary-color);
}

.social-links {
    display: flex;
    justify-content: center;
    margin-top: 50px;
    display: none; /* Hidden initially */
}

.social-links a {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    margin: 0 10px;
    font-size: 20px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

footer {
    background-color: var(--dark-color);
    color: white;
    padding: 30px 0;
    text-align: center;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.footer-logo {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
    color: white;
}

.footer-links {
    margin-bottom: 20px;
}

.footer-links a {
    color: white;
    margin: 0 15px;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--accent-color);
}

.copyright {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0px;
}

.build-by {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.footer-contact p {
    font-size: 15px;
    color: rgba(255, 255, 255, 0.9);
}

.footer-contact a {
    color: var(--accent-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-contact a:hover {
    color: white;
    text-decoration: underline;
}

.subscription-form {
    max-width: 500px;
    margin: 0 auto 40px;
}

.form-control {
    height: 50px;
    border-radius: 25px;
    padding: 0 20px;
    font-size: 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-align: center;
}

/* Center placeholder text in all browsers */
.form-control::placeholder {
    text-align: center;
}

/* For Internet Explorer */
.form-control:-ms-input-placeholder {
    text-align: center;
}

/* For Edge */
.form-control::-ms-input-placeholder {
    text-align: center;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

#form-message {
    padding: 15px;
    border-radius: 10px;
    font-weight: 500;
}

#form-message.success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

#form-message.error {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.alert {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    border-color: rgba(13, 202, 240, 0.2);
    color: #1d5d91;
}

.alert-heading {
    color: #1d5d91;
    font-weight: 600;
    margin-bottom: 10px;
}

/* Feature Tabs Styles */
.feature-tabs {
    max-width: 900px;
    margin: 0 auto;
}

.nav-tabs {
    border-bottom: 2px solid rgba(74, 111, 165, 0.2);
}

.nav-tabs .nav-link {
    color: var(--text-color);
    border: none;
    padding: 12px 20px;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    margin-bottom: -2px;
}

.nav-tabs .nav-link:hover {
    border-color: rgba(74, 111, 165, 0.5);
    background-color: rgba(74, 111, 165, 0.05);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

.feature-content {
    padding: 30px 20px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 0 0 10px 10px;
}

.feature-item {
    text-align: center;
    padding: 25px 15px;
    border-radius: 10px;
    background-color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    font-size: 28px;
}

.feature-item h5 {
    margin-bottom: 15px;
    color: var(--secondary-color);
    font-weight: 600;
}

.feature-item p {
    color: var(--text-color);
    font-size: 14px;
    line-height: 1.6;
}

@media (max-width: 768px) {
    h1 {
        font-size: 36px;
    }

    .tagline {
        font-size: 20px;
    }

    .description {
        font-size: 16px;
    }

    .hero-logo {
        width: 150px;
        height: 150px;
        font-size: 60px;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 28px;
    }

    .tagline {
        font-size: 18px;
    }

    .hero-logo {
        width: 120px;
        height: 120px;
        font-size: 48px;
    }

    .cta-button {
        padding: 10px 25px;
        font-size: 16px;
    }
}
