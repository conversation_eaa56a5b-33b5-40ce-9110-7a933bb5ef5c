// Email window function
function openMailWindow(subject = '') {
    let mailUrl = "mailto:<EMAIL>";
    if (subject) {
        mailUrl += "?subject=" + encodeURIComponent(subject);
    }
    const newWindow = window.open("", "_blank", "width=400,height=300");
    newWindow.document.write("<h1>Email Us</h1><p>Click <a href='" + mailUrl + "'>here</a> to send an email to smarch [ @ ] valleyberg.com.</p>");
    if (subject) {
        newWindow.document.write("<p>Subject: " + subject + "</p>");
    }
}

// Form submission handling
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('subscription-form');
    const formMessage = document.getElementById('form-message');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            const name = formData.get('name') || 'Not provided';
            const email = formData.get('email');

            if (!email) {
                formMessage.className = 'error';
                formMessage.textContent = 'Please enter your email address.';
                formMessage.style.display = 'block';
                return;
            }

            // Send to Google Sheets
            const scriptURL = 'https://script.google.com/macros/s/AKfycbxdtaj_su08F4voHmJnFjl0bYluZj33Cw-S-g7W7o4i3kQ6Y95H3NS4mTO16HhMCuuJnA/exec';

            // Prepare data for Google Sheets
            const sheetData = new FormData();
            sheetData.append('name', name);
            sheetData.append('email', email);
            sheetData.append('timestamp', new Date().toISOString());

            fetch(scriptURL, {
                method: 'POST',
                body: sheetData
            })
            .then(response => {
                // Success
                formMessage.className = 'success';
                formMessage.textContent = 'Thank you for subscribing! We\'ll notify you when we launch.';
                formMessage.style.display = 'block';
                form.reset();

                // Send confirmation email
                sendConfirmationEmail(email, name);
            })
            .catch(error => {
                console.error('Error:', error);
                formMessage.className = 'error';
                formMessage.textContent = 'Oops! There was a problem with your submission. Please try again.';
                formMessage.style.display = 'block';
            });
        });
    }

    function sendConfirmationEmail(email, name) {
        // This function will send a confirmation email to the subscriber
        // You can implement this using Email.js or another service

        // For now, we'll just log it to the console
        console.log(`Would send confirmation to: ${email} (${name})`);

        // Here's how you would set up Email.js:
        /*
        // Load Email.js script dynamically
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js';
        script.onload = function() {
            emailjs.init('YOUR_USER_ID'); // Replace with your Email.js user ID

            // Send the email
            emailjs.send('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', {
                to_name: name,
                to_email: email,
                subject: 'Thanks for subscribing to Smarch!',
                message: 'We\'ll notify you when we launch.'
            }).then(function(response) {
                console.log('Email sent:', response);
            }, function(error) {
                console.error('Email error:', error);
            });
        };
        document.head.appendChild(script);
        */
    }
});

/*
⚠️ IMPORTANT: HOW TO SET UP GOOGLE SHEETS FOR EMAIL COLLECTION ⚠️

1. Create a new Google Sheet with columns: Timestamp, Name, Email
2. Go to Extensions > Apps Script
3. Replace the code with this:

function doPost(e) {
  var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  var data = e.parameter;
  sheet.appendRow([data.timestamp, data.name, data.email]);
  return ContentService.createTextOutput(JSON.stringify({result: 'success'})).setMimeType(ContentService.MimeType.JSON);
}

4. Save the script and deploy it as a web app
5. Set "Who has access" to "Anyone, even anonymous"
6. Copy the web app URL and update the scriptURL variable in this file
7. The form will now save submissions to your Google Sheet

For email confirmations:
- Sign up at EmailJS.com (free tier available)
- Create a template for confirmation emails
- Uncomment and configure the EmailJS code in the sendConfirmationEmail function
*/
