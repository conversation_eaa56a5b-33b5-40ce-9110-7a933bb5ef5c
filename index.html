<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Rafal Zygula - Strategic Solution Designer transforming complex business challenges into implementable solutions">
    <meta name="keywords" content="strategic consulting, solution design, business transformation, innovation management">
    <meta name="author" content="Rafal Zygula">
    <meta name="theme-color" content="#1a1a1a">

    <!-- Open Graph -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Valleyberg Strategic Consulting - Rafal Zygula">
    <meta property="og:description" content="Transforming complex business challenges into implementable solutions through systematic research and strategic design">
    <meta property="og:image" content="assets/images/valleyberg-logo.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Libre+Caslon+Text:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet">
    
    <!-- Bootstrap & Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Favicons -->
    <link rel="icon" type="image/png" href="assets/images/valleyberg-logo.png" sizes="96x96">
    <link rel="shortcut icon" href="assets/images/valleyberg-logo.png">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/valleyberg-logo.png">
    <meta name="apple-mobile-web-app-title" content="Valleyberg Group">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    
    <title>Rafal Zygula - Strategic Solution Designer | Valleyberg</title>
    
    <style>
        :root {
            --primary-dark: #1a1a1a;
            --secondary-dark: #2d2d2d;
            --accent-gold: #d4af37;
            --accent-blue: #4a90e2;
            --text-light: #f8f9fa;
            --text-muted: #adb5bd;
            --gradient-primary: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            --gradient-accent: linear-gradient(135deg, #d4af37 0%, #4a90e2 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--primary-dark);
            color: var(--text-light);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .serif-font {
            font-family: 'Libre Caslon Text', serif;
        }

        /* Navigation */
        .navbar {
            background: rgba(26, 26, 26, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(212, 175, 55, 0.1);
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-family: 'Libre Caslon Text', serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-gold) !important;
        }

        .nav-link {
            color: var(--text-light) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--accent-gold) !important;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 300;
            margin-bottom: 1.5rem;
            background: var(--gradient-accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero .tagline {
            font-size: 1.3rem;
            color: var(--text-muted);
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .methodology-box {
            background: rgba(45, 45, 45, 0.8);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
            backdrop-filter: blur(10px);
        }

        .methodology-text {
            font-family: 'Libre Caslon Text', serif;
            font-size: 1.1rem;
            color: var(--accent-gold);
            font-style: italic;
        }

        /* Buttons */
        .btn-primary-custom {
            background: var(--gradient-accent);
            border: none;
            padding: 12px 30px;
            border-radius: 30px;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
            color: white;
        }

        .btn-outline-custom {
            border: 2px solid var(--accent-gold);
            color: var(--accent-gold);
            padding: 10px 28px;
            border-radius: 30px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            background: transparent;
        }

        .btn-outline-custom:hover {
            background: var(--accent-gold);
            color: var(--primary-dark);
        }

        /* Sections */
        .section {
            padding: 6rem 0;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 3rem;
            text-align: center;
            background: var(--gradient-accent);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: var(--text-muted);
            text-align: center;
            margin-bottom: 4rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Case Studies */
        .case-study-card {
            background: rgba(45, 45, 45, 0.8);
            border-radius: 16px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(212, 175, 55, 0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .case-study-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(212, 175, 55, 0.3);
        }

        .case-study-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--accent-gold);
            margin-bottom: 0.5rem;
        }

        .case-study-subtitle {
            color: var(--text-muted);
            font-style: italic;
            margin-bottom: 1.5rem;
        }

        .case-study-section {
            margin-bottom: 1.5rem;
        }

        .case-study-section h6 {
            color: var(--accent-blue);
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .impact-metrics {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .metric {
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            flex: 1;
            min-width: 150px;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-gold);
            display: block;
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-muted);
            margin-top: 0.3rem;
        }

        /* About Section */
        .about-section {
            background: rgba(45, 45, 45, 0.3);
        }

        .profile-image {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid var(--accent-gold);
            margin-bottom: 2rem;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .skill-category {
            background: rgba(45, 45, 45, 0.8);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(212, 175, 55, 0.1);
        }

        .skill-category h6 {
            color: var(--accent-gold);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .skill-category p {
            color: var(--text-muted);
            margin: 0;
            line-height: 1.8;
        }

        /* Projects Section */
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .project-card {
            background: rgba(45, 45, 45, 0.8);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid rgba(212, 175, 55, 0.1);
            transition: all 0.3s ease;
            text-align: center;
        }

        .project-card:hover {
            transform: translateY(-5px);
            border-color: rgba(212, 175, 55, 0.3);
        }

        .project-icon {
            font-size: 3rem;
            color: var(--accent-gold);
            margin-bottom: 1rem;
        }

        .project-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .project-description {
            color: var(--text-muted);
            margin-bottom: 1.5rem;
        }

        .project-status {
            display: inline-block;
            padding: 0.3rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-live {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status-development {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        /* Contact Section */
        .contact-section {
            background: var(--gradient-primary);
            text-align: center;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
            margin-top: 2rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            color: var(--text-light);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .contact-item:hover {
            color: var(--accent-gold);
        }

        .contact-item i {
            font-size: 1.2rem;
            color: var(--accent-gold);
        }

        /* Footer */
        .footer {
            background: var(--primary-dark);
            padding: 2rem 0;
            border-top: 1px solid rgba(212, 175, 55, 0.1);
            text-align: center;
        }

        /* Animations */
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in-up.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero .tagline {
                font-size: 1.1rem;
            }
            
            .section {
                padding: 4rem 0;
            }
            
            .case-study-card {
                padding: 1.5rem;
            }
            
            .impact-metrics {
                flex-direction: column;
            }
            
            .contact-info {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">Valleyberg</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="#case-studies">Case Studies</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
                    <li class="nav-item"><a class="nav-link" href="#projects">Projects</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="hero-content fade-in-up">
                        <h1>Strategic Solution Designer</h1>
                        <p class="tagline">Transforming complex business challenges into implementable solutions through systematic research and strategic design.</p>
                        
                        <div class="d-flex gap-3 flex-wrap mb-4">
                            <a href="#contact" class="btn-primary-custom">Start a Project</a>
                            <a href="#case-studies" class="btn-outline-custom">View Case Studies</a>
                            <a href="#projects" class="btn-outline-custom">Projects</a>
                        </div>

                        <div class="methodology-box fade-in-up">
                            <p class="methodology-text mb-0">
                                Problem Discovery → Deep Research & Analysis → Solution Architecture → Prototype Development → Strategic Implementation
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Case Studies Section -->
    <section id="case-studies" class="section">
        <div class="container">
            <div class="fade-in-up">
                <h2 class="section-title">Strategic Case Studies</h2>
                <p class="section-subtitle">Real business challenges solved through systematic research and innovative solution design</p>
            </div>

            <!-- SMARCH Case Study -->
            <div class="case-study-card fade-in-up">
                <h3 class="case-study-title">SMARCH Talent Platform</h3>
                <p class="case-study-subtitle">Reimagining Recruitment Through Strategic Research</p>
                
                <div class="row">
                    <div class="col-lg-8">
                        <div class="case-study-section">
                            <h6>The Challenge</h6>
                            <p>Fast-scaling companies struggle with inefficient recruitment processes, experiencing high time-to-hire, poor candidate matches, and frustrated hiring teams. Traditional recruitment platforms fail to address the collaborative nature of modern hiring decisions.</p>
                        </div>

                        <div class="case-study-section">
                            <h6>Research Approach</h6>
                            <ul>
                                <li>50+ stakeholder interviews with hiring managers, recruiters, and candidates</li>
                                <li>Comprehensive competitive analysis of 15+ recruitment platforms</li>
                                <li>Process mapping across 12 companies</li>
                                <li>Identified 8 critical bottlenecks in traditional recruitment</li>
                            </ul>
                        </div>

                        <div class="case-study-section">
                            <h6>Solution Architecture</h6>
                            <p>Designed a collaborative decision-making platform with real-time stakeholder alignment, transparent candidate journeys, AI-powered matching based on team dynamics, and integrated communication systems.</p>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="impact-metrics">
                            <div class="metric">
                                <span class="metric-value">45%</span>
                                <span class="metric-label">Reduction in time-to-hire</span>
                            </div>
                            <div class="metric">
                                <span class="metric-value">85%</span>
                                <span class="metric-label">Improved team coordination</span>
                            </div>
                            <div class="metric">
                                <span class="metric-value">+$757B</span>
                                <span class="metric-label">Market opportunity</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Game Studio Case Study -->
            <div class="case-study-card fade-in-up">
                <h3 class="case-study-title">Game Studio Efficiency Optimization</h3>
                <p class="case-study-subtitle">Transforming Team Performance Through System Design</p>
                
                <div class="row">
                    <div class="col-lg-8">
                        <div class="case-study-section">
                            <h6>The Challenge</h6>
                            <p>A 15-person cross-functional game development team experienced communication breakdowns, unclear workflows, and declining productivity. Traditional project management approaches weren't addressing underlying systemic issues.</p>
                        </div>

                        <div class="case-study-section">
                            <h6>Solution Framework</h6>
                            <p>Designed cross-functional information sharing protocols, structured feedback loops, quality assessment frameworks, and performance measurement systems with clear escalation pathways.</p>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="impact-metrics">
                            <div class="metric">
                                <span class="metric-value">30%</span>
                                <span class="metric-label">Efficiency improvement</span>
                            </div>
                            <div class="metric">
                                <span class="metric-value">50%</span>
                                <span class="metric-label">Reduced delays</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Regional Development Case Study -->
            <div class="case-study-card fade-in-up">
                <h3 class="case-study-title">Regional Development Strategy</h3>
                <p class="case-study-subtitle">Master's Thesis: 1M Population Regional Economic Transformation</p>
                
                <div class="row">
                    <div class="col-lg-8">
                        <div class="case-study-section">
                            <h6>The Challenge</h6>
                            <p>Develop a comprehensive regional development strategy for a 1 million inhabitants region in Poland, focusing on sustainable economic growth, job market improvement, and enhanced social well-being through strategic resource utilization.</p>
                        </div>

                        <div class="case-study-section">
                            <h6>Research Methodology</h6>
                            <p class="text-light">Extensive analysis of EU regional development documentation • Quantitative and qualitative research methods • Critical analysis of existing economic structures • Stakeholder needs assessment • Resource mapping and optimization strategies</p>
                        </div>

                        <div class="case-study-section">
                            <h6>Solution Framework</h6>
                            <p>Designed comprehensive regional development strategy consisting of 15 interconnected cooperative projects, addressing job market transformation, resource optimization, social infrastructure development, and sustainable economic growth mechanisms.</p>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="impact-metrics">
                            <div class="metric">
                                <span class="metric-value">1M</span>
                                <span class="metric-label">Population impact</span>
                            </div>
                            <div class="metric">
                                <span class="metric-value">15</span>
                                <span class="metric-label">Cooperative projects</span>
                            </div>
                            <div class="metric">
                                <span class="metric-value">MSc</span>
                                <span class="metric-label">Academic thesis</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PropTech Case Study -->
            <div class="case-study-card fade-in-up">
                <h3 class="case-study-title">PropTech Growth Strategy</h3>
                <p class="case-study-subtitle">4-Year Strategic Market Analysis for iQuest AB</p>
                
                <div class="row">
                    <div class="col-lg-8">
                        <div class="case-study-section">
                            <h6>Strategic Framework</h6>
                            <p>Delivered comprehensive 42-point strategic framework using MIRO visualization, 4-year development roadmap, market positioning focused on sustainability compliance, and partnership strategy with complementary providers.</p>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="impact-metrics">
                            <div class="metric">
                                <span class="metric-value">€2.3M</span>
                                <span class="metric-label">Revenue opportunity</span>
                            </div>
                            <div class="metric">
                                <span class="metric-value">4 Years</span>
                                <span class="metric-label">Strategic roadmap</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="section about-section">
        <div class="container">
            <div class="fade-in-up">
                <h2 class="section-title">Strategic Expertise</h2>
                <p class="section-subtitle">MSc in Managerial Economics with 5+ years of consulting experience across gaming, PropTech, recruitment, and sustainability sectors</p>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="skills-grid fade-in-up">
                        <div class="skill-category">
                            <h6><i class="fas fa-search me-2"></i>Research Methods</h6>
                            <p>Qualitative Research • Stakeholder Interviews • User Journey Mapping • Competitive Intelligence • Data Mining • Performance Metrics • Market Sizing</p>
                        </div>
                        <div class="skill-category">
                            <h6><i class="fas fa-cogs me-2"></i>Solution Design</h6>
                            <p>Strategic Frameworks • Solution Architecture • System Design • Process Optimization • Business Model Design • User Experience Design</p>
                        </div>
                        <div class="skill-category">
                            <h6><i class="fas fa-chart-line me-2"></i>Implementation</h6>
                            <p>Stakeholder Alignment • Executive Presentations • Workshop Facilitation • Project Planning • Performance Measurement • Change Management</p>
                        </div>
                        <div class="skill-category">
                            <h6><i class="fas fa-laptop-code me-2"></i>Technical Proficiency</h6>
                            <p>Python • Django • AI Integration • MIRO • Figma • JIRA • Advanced Analytics • Full-Stack Development • SaaS Architecture</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="section">
        <div class="container">
            <div class="fade-in-up">
                <h2 class="section-title">Active Projects</h2>
                <p class="section-subtitle">Current ventures and platforms under the Valleyberg umbrella</p>
            </div>

            <div class="projects-grid">
                <div class="project-card fade-in-up">
                    <div class="project-icon">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <h4 class="project-title">TEMPUS Author Platform</h4>
                    <p class="project-description">Complete publishing ecosystem for authors, featuring integrated community engagement and digital platform solutions.</p>
                    <div class="project-status status-live">Live Platform</div>
                    <div class="mt-3">
                        <a href="https://tempus.valleyberg.com" class="btn-outline-custom" target="_blank">Visit Platform</a>
                    </div>
                </div>

                <div class="project-card fade-in-up">
                    <div class="project-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4 class="project-title">SMARCH</h4>
                    <p class="project-description">Recruitment Collaboration Hub transforming how teams make hiring decisions through collaborative workflows.</p>
                    <div class="project-status status-development">In Development</div>
                    <div class="mt-3">
                        <a href="smarch.html" class="btn-outline-custom" target="_blank">Learn More</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section contact-section">
        <div class="container">
            <div class="fade-in-up">
                <h2 class="section-title">Let's Solve Complex Challenges Together</h2>
                <p class="section-subtitle">Available for strategic consulting, innovation management, and product strategy engagements</p>
                
                <div class="contact-info">
                    <a href="mailto:<EMAIL>" class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <EMAIL>
                    </a>
                    <a href="https://linkedin.com/in/rafal-zygula" class="contact-item" target="_blank">
                        <i class="fab fa-linkedin"></i>
                        LinkedIn Profile
                    </a>
                </div>

                <div class="mt-4">
                    <p>Stockholm, Sweden • Remote/EU Flexible</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="text-muted mb-0">© 2025 Valleyberg Group. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
    
    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in-up').forEach(el => {
            observer.observe(el);
        });

        // Navbar transparency on scroll
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(26, 26, 26, 0.98)';
            } else {
                navbar.style.background = 'rgba(26, 26, 26, 0.95)';
            }
        });
    </script>
</body>
</html>