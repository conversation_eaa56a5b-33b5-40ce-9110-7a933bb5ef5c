// Function to save the filled form as HTML
function saveAsHTML() {
    // Get all editable fields
    const editableFields = document.querySelectorAll('.editable-field');
    
    // Check if any fields are still showing default values
    let defaultValuesRemain = false;
    editableFields.forEach(field => {
        if (field.textContent.includes('[') && field.textContent.includes(']')) {
            defaultValuesRemain = true;
        }
    });
    
    // Warn user if default values remain
    if (defaultValuesRemain) {
        if (!confirm('Some fields still contain default values. Continue anyway?')) {
            return;
        }
    }
    
    // Create a copy of the current document
    const documentClone = document.documentElement.cloneNode(true);
    
    // Find the save and print buttons in the clone and remove them
    const buttonContainer = documentClone.querySelector('.button-container');
    if (buttonContainer) {
        buttonContainer.parentNode.removeChild(buttonContainer);
    }
    
    // Find the instruction paragraphs and remove them
    const instructionParagraphs = documentClone.querySelectorAll('p[style*="text-align: center; margin-bottom: 1.5rem;"]');
    instructionParagraphs.forEach(paragraph => {
        paragraph.parentNode.removeChild(paragraph);
    });
    
    // Make editable fields non-editable in the saved version and remove styling
    const cloneEditableFields = documentClone.querySelectorAll('.editable-field');
    cloneEditableFields.forEach(field => {
        field.removeAttribute('contenteditable');
        field.classList.remove('editable-field');
        field.style.borderBottom = 'none';
        field.style.padding = '0';
        field.style.color = 'inherit';
    });
    
    // Create a Blob with the HTML content
    const blob = new Blob([documentClone.outerHTML], { type: 'text/html' });
    
    // Create a download link
    const downloadLink = document.createElement('a');
    downloadLink.href = URL.createObjectURL(blob);
    downloadLink.download = 'Smarch-LOI-Filled.html';
    
    // Trigger the download
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}
