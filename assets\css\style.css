body {
    background-color: #000000;
    position: relative;
}

/* Add overlay to entire page */
body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: -1;
}

main {
    width: 100%;
    letter-spacing: 0.2rem;
    font-family: lato;
    text-shadow: 0 0 12px rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    position: relative;
    z-index: 1;
}

.group-container {
    height: 300px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    width: 100%;
    margin: 0 auto;
    position: relative;
}

.logo-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    position: absolute;
    z-index: -1;
    scale: 0.5;
}

.row.equal-height {
    display: flex;
    flex-wrap: wrap;
}

.row.equal-height > [class*='col-'] {
    display: flex;
    flex-direction: column;
}

.publishing-house-container, .innovations-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    margin-top: 1rem;
    height: 250px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    width: 100%;
    transition: transform 0.3s ease;
    border-radius: 8px;
    position: relative;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-image: linear-gradient(to right, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0)) 1;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
    padding: 0;
    overflow: hidden;
}

.publishing-house-container:hover, .innovations-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.publishinghouse-brandname, .innovations-brandname {
    color: #ffffff;
    padding: 20px 15px;
    border-radius: 5px;
    font-weight: 600;
    text-align: center;
    width: 100%;
    margin-bottom: 20px;
    background-color: rgba(0, 0, 0, 0.3);
}

.brandname {
    color: #ffffff;
    padding: 60px 20px;
    border-radius: 5px;
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    max-width: 100%;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
}

.tempus-author-platform-link {
    text-decoration: none;
    color: #ffffff;
    display: inline-block;
    padding: 10px 20px;
    border-radius: 5px;
    margin-top: 10px;
}

.tempus-author-platform-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    transform: scale(1.05);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.tempus-author-platform-logo {
    font-family: 'Libre Caslon Text', serif;
    font-weight: 400;
    text-align: center;
    max-width: 100%;
    text-transform: capitalize;
    font-size: 1.8rem;
    position: relative;
}

.link-indicator {
    font-size: 0.8rem;
    margin-left: 5px;
    opacity: 0.7;
    display: inline-block;
    transition: all 0.3s ease;
}

.tempus-author-platform-link:hover .link-indicator {
    opacity: 1;
    transform: translateX(3px);
}

.smarch-logo-set {
    padding: 15px;
    position: relative;
    transition: all 0.3s ease;
    cursor: default;
    margin-top: 10px;
}

.smarch-logo-set:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

.smarch-logo {
    font-family: 'Libre Caslon Text', serif;
    font-weight: 300;
    text-align: center;
    max-width: 100%;
    text-transform: lowercase;
    font-size: 3rem;
    margin-bottom: 0;
    line-height: 1;
}

.smarch-logo-sub {
    font-family: 'Libre Caslon Text', serif;
    font-weight: 300;
    text-align: center;
    max-width: 100%;
    text-transform: lowercase;
    font-size: 0.7rem;
    margin-top: 0;
    line-height: 1;
    letter-spacing: 0.1rem;
}

.coming-soon-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: rgba(255, 215, 0, 0.9);
    color: #000;
    font-size: 0.7rem;
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(0, 0, 0, 0.2);
    z-index: 2;
}

a {
    color: #ffffff;
    text-decoration: none;
} 

@media (max-width: 992px) {
    main {
        margin-top: 4rem;
    }
    
    .brandname {
        font-size: 2.5rem;
        padding: 40px 15px;
        letter-spacing: 0.15rem;
    }
    
    .group-container {
        height: 250px;
    }
    
    .publishing-house-container, .innovations-container {
        height: 250px;
    }

    .tempus-author-platform-logo {
        font-size: 1.5rem;
        letter-spacing: 0.1rem;
        text-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
    }
    
    .smarch-logo {
        font-size: 2.2rem;
    }
    
    .smarch-logo-sub {
        font-size: 0.6rem;
    }
}

@media (max-width: 576px) {
    main {
        margin-top: 3rem;
    }
    
    .brandname {
        font-size: 2rem;
        padding: 30px 10px;
        letter-spacing: 0.1rem;
    }
    
    .group-container {
        height: 200px;
    }
    
    .publishing-house-container, .innovations-container {
        height: 220px;
        margin-top: 0.5rem;
    }
    
    .tempus-author-platform-logo {
        font-size: 1.3rem;
    }
    
    .smarch-logo {
        font-size: 2rem;
    }
    
    .smarch-logo-sub {
        font-size: 0.5rem;
    }
    
    .coming-soon-badge {
        font-size: 0.6rem;
        padding: 4px 8px;
    }
}

/* Footer styling */
footer {
    background-color: rgba(0, 0, 0, 0.8) !important;
    color: #999 !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

footer p, footer small {
    color: #999 !important;
}

footer hr {
    border-color: rgba(255, 255, 255, 0.1);
}